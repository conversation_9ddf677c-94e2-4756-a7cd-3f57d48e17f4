'use client';

import React, { useEffect, useRef, forwardRef } from "react";
// @ts-ignore
import { <PERSON><PERSON><PERSON>, Program, Mesh, Triangle, Vec3 } from "ogl";
import { cn } from '../../../../lib/utils';

export interface OrbProps extends React.HTMLAttributes<HTMLDivElement> {
  hue?: number;
  hoverIntensity?: number;
  rotateOnHover?: boolean;
  forceHoverState?: boolean;
  // Simplified props for enhanced voice interface
  voiceState?: 'idle' | 'listening';
  emotionIntensity?: number; // 0.0 - 1.0 for emotion-based effects
}

export const Orb = forwardRef<HTMLDivElement, OrbProps>(({
  className,
  hue = 0,
  hoverIntensity = 0.2,
  rotateOnHover = true,
  forceHoverState = false,
  voiceState = 'idle',
  emotionIntensity = 0.3,
  ...props
}, ref) => {
  const domProps = { ...props };
  delete (domProps as any).hue;
  delete (domProps as any).hoverIntensity;
  delete (domProps as any).rotateOnHover;
  delete (domProps as any).forceHoverState;
  delete (domProps as any).voiceState;
  delete (domProps as any).emotionIntensity;

  const ctnDom = useRef<HTMLDivElement>(null);
  const programRef = useRef<any>(null);
  const rendererRef = useRef<any>(null);
  const meshRef = useRef<any>(null);
  const lastHueRef = useRef<number>(hue);
  const lastIntensityRef = useRef<number>(hoverIntensity);
  const lastVoiceStateRef = useRef<string>(voiceState);
  const lastEmotionIntensityRef = useRef<number>(emotionIntensity);

  const vert = /* glsl */ `
    precision highp float;
    attribute vec2 position;
    attribute vec2 uv;
    varying vec2 vUv;
    void main() {
      vUv = uv;
      gl_Position = vec4(position, 0.0, 1.0);
    }
  `;

  const frag = /* glsl */ `
    precision highp float;

    uniform float iTime;
    uniform vec3 iResolution;
    uniform float hue;
    uniform float hover;
    uniform float rot;
    uniform float hoverIntensity;
    // New uniforms for enhanced voice interface
    uniform float voiceStateFloat; // 0.0=idle, 1.0=listening
    uniform float emotionIntensity; // 0.0 - 1.0 for emotion effects
    varying vec2 vUv;

    vec3 rgb2yiq(vec3 c) {
      float y = dot(c, vec3(0.299, 0.587, 0.114));
      float i = dot(c, vec3(0.596, -0.274, -0.322));
      float q = dot(c, vec3(0.211, -0.523, 0.312));
      return vec3(y, i, q);
    }
    
    vec3 yiq2rgb(vec3 c) {
      float r = c.x + 0.956 * c.y + 0.621 * c.z;
      float g = c.x - 0.272 * c.y - 0.647 * c.z;
      float b = c.x - 1.106 * c.y + 1.703 * c.z;
      return vec3(r, g, b);
    }
    
    vec3 adjustHue(vec3 color, float hueDeg) {
      float hueRad = hueDeg * 3.14159265 / 180.0;
      vec3 yiq = rgb2yiq(color);
      float cosA = cos(hueRad);
      float sinA = sin(hueRad);
      float i = yiq.y * cosA - yiq.z * sinA;
      float q = yiq.y * sinA + yiq.z * cosA;
      yiq.y = i;
      yiq.z = q;
      return yiq2rgb(yiq);
    }
    
    vec3 hash33(vec3 p3) {
      p3 = fract(p3 * vec3(0.1031, 0.11369, 0.13787));
      p3 += dot(p3, p3.yxz + 19.19);
      return -1.0 + 2.0 * fract(vec3(
        p3.x + p3.y,
        p3.x + p3.z,
        p3.y + p3.z
      ) * p3.zyx);
    }
    
    float snoise3(vec3 p) {
      const float K1 = 0.333333333;
      const float K2 = 0.166666667;
      vec3 i = floor(p + (p.x + p.y + p.z) * K1);
      vec3 d0 = p - (i - (i.x + i.y + i.z) * K2);
      vec3 e = step(vec3(0.0), d0 - d0.yzx);
      vec3 i1 = e * (1.0 - e.zxy);
      vec3 i2 = 1.0 - e.zxy * (1.0 - e);
      vec3 d1 = d0 - (i1 - K2);
      vec3 d2 = d0 - (i2 - K1);
      vec3 d3 = d0 - 0.5;
      vec4 h = max(0.6 - vec4(
        dot(d0, d0),
        dot(d1, d1),
        dot(d2, d2),
        dot(d3, d3)
      ), 0.0);
      vec4 n = h * h * h * h * vec4(
        dot(d0, hash33(i)),
        dot(d1, hash33(i + i1)),
        dot(d2, hash33(i + i2)),
        dot(d3, hash33(i + 1.0))
      );
      return dot(vec4(31.316), n);
    }
    
    vec4 extractAlpha(vec3 colorIn) {
      float a = max(max(colorIn.r, colorIn.g), colorIn.b);
      return vec4(colorIn.rgb / (a + 1e-5), a);
    }
    
    const vec3 baseColor1 = vec3(0.611765, 0.262745, 0.996078);
    const vec3 baseColor2 = vec3(0.298039, 0.760784, 0.913725);
    const vec3 baseColor3 = vec3(0.062745, 0.078431, 0.600000);
    const float innerRadius = 0.4; // Reduced from 0.6 to make orb thicker
    const float noiseScale = 0.65;
    
    float light1(float intensity, float attenuation, float dist) {
      return intensity / (1.0 + dist * attenuation);
    }
    
    float light2(float intensity, float attenuation, float dist) {
      return intensity / (1.0 + dist * dist * attenuation);
    }
    
    vec4 draw(vec2 uv) {
      vec3 color1 = adjustHue(baseColor1, hue);
      vec3 color2 = adjustHue(baseColor2, hue);
      vec3 color3 = adjustHue(baseColor3, hue);

      float ang = atan(uv.y, uv.x);
      float len = length(uv);
      float invLen = len > 0.0 ? 1.0 / len : 0.0;

      // Enhanced noise for voice states
      float timeScale = 0.5;
      float noiseIntensity = 1.0;

      // Voice state modifications - simplified to idle and listening only
      if (voiceStateFloat > 0.5) {
        // Listening state - more active, breathing-like motion
        timeScale = 0.8 + emotionIntensity * 0.3;
        noiseIntensity = 1.2 + emotionIntensity * 0.3;
      } else {
        // Idle state - smooth, gentle animation
        timeScale = 0.5 + emotionIntensity * 0.2;
        noiseIntensity = 1.0 + emotionIntensity * 0.2;
      }

      float n0 = snoise3(vec3(uv * noiseScale, iTime * timeScale)) * 0.5 + 0.5;

      // Enhanced radius calculation with voice state effects
      float baseRadius = mix(innerRadius, 1.0, 0.4);
      float maxRadius = mix(innerRadius, 1.0, 0.6);

      float r0 = mix(baseRadius, maxRadius, n0 * noiseIntensity);
      float d0 = distance(uv, (r0 * invLen) * uv);
      float v0 = light1(1.0, 10.0, d0);
      v0 *= smoothstep(r0 * 1.05, r0, len);

      // Enhanced color mixing with emotion intensity
      float colorMixSpeed = 2.0 + emotionIntensity * 2.0;
      float cl = cos(ang + iTime * colorMixSpeed) * 0.5 + 0.5;

      // Dynamic light source with voice state effects
      float lightSpeed = -1.0;
      if (voiceStateFloat > 0.5) {
        lightSpeed = -0.7; // Slightly slower for listening
      }

      float a = iTime * lightSpeed;
      vec2 pos = vec2(cos(a), sin(a)) * r0;
      float d = distance(uv, pos);
      float v1 = light2(1.5, 5.0, d);
      v1 *= light1(1.0, 50.0, d0);

      // Enhanced intensity based on voice state and emotion
      float intensityMultiplier = 1.0 + emotionIntensity * 0.4;

      // High emotion intensity creates additional glow effect
      if (emotionIntensity > 0.7) {
        float glowPulse = sin(iTime * 4.0 + emotionIntensity * 8.0) * 0.2 + 0.8;
        intensityMultiplier *= glowPulse;
      }

      float v2 = smoothstep(1.0, mix(innerRadius, 1.0, n0 * 0.5), len);
      float v3 = smoothstep(innerRadius, mix(innerRadius, 1.0, 0.5), len);

      vec3 col = mix(color1, color2, cl);
      col = mix(color3, col, v0);
      col = (col + v1) * v2 * v3 * intensityMultiplier;
      col = clamp(col, 0.0, 1.0);

      return extractAlpha(col);
    }
    
    vec4 mainImage(vec2 fragCoord) {
      vec2 center = iResolution.xy * 0.5;
      float size = min(iResolution.x, iResolution.y);
      vec2 uv = (fragCoord - center) / size * 2.0;

      float angle = rot;
      float s = sin(angle);
      float c = cos(angle);
      uv = vec2(c * uv.x - s * uv.y, s * uv.x + c * uv.y);

      // Enhanced distortion effects based on voice state
      float distortionIntensity = hover * hoverIntensity * 0.1;

      // Voice state specific distortions - simplified to idle and listening
      if (voiceStateFloat > 0.5) {
        // Listening state - gentle breathing distortion
        distortionIntensity += 0.03 * sin(iTime * 2.0);
        uv.x += distortionIntensity * sin(uv.y * 8.0 + iTime * 1.5);
        uv.y += distortionIntensity * sin(uv.x * 8.0 + iTime * 1.5);
      } else {
        // Idle state - original hover distortion
        uv.x += distortionIntensity * sin(uv.y * 10.0 + iTime);
        uv.y += distortionIntensity * sin(uv.x * 10.0 + iTime);
      }

      // Emotion-based subtle distortion
      float emotionDistortion = emotionIntensity * 0.02;
      uv.x += emotionDistortion * sin(uv.y * 20.0 + iTime * 0.5);
      uv.y += emotionDistortion * cos(uv.x * 20.0 + iTime * 0.5);

      return draw(uv);
    }
    
    void main() {
      vec2 fragCoord = vUv * iResolution.xy;
      vec4 col = mainImage(fragCoord);
      gl_FragColor = vec4(col.rgb * col.a, col.a);
    }
  `;

  // Initialize WebGL context once
  useEffect(() => {
    const container = ctnDom.current;
    if (!container) return;

    const renderer = new Renderer({ alpha: true, premultipliedAlpha: false });
    const gl = renderer.gl;
    gl.clearColor(0, 0, 0, 0);
    container.appendChild(gl.canvas);

    const geometry = new Triangle(gl);
    const program = new Program(gl, {
      vertex: vert,
      fragment: frag,
      uniforms: {
        iTime: { value: 0 },
        iResolution: {
          value: new Vec3(
            gl.canvas.width,
            gl.canvas.height,
            gl.canvas.width / gl.canvas.height
          ),
        },
        hue: { value: hue },
        hover: { value: 0 },
        rot: { value: 0 },
        hoverIntensity: { value: hoverIntensity },
        // New uniforms for enhanced voice interface
        voiceStateFloat: { value: voiceState === 'listening' ? 1.0 : 0.0 },
        emotionIntensity: { value: emotionIntensity },
      },
    });

    const mesh = new Mesh(gl, { geometry, program });

    // Store references for later use
    rendererRef.current = renderer;
    programRef.current = program;
    meshRef.current = mesh;

    function resize() {
      if (!container || !renderer || !program) return;
      const dpr = window.devicePixelRatio || 1;
      const width = container.clientWidth;
      const height = container.clientHeight;
      renderer.setSize(width * dpr, height * dpr);
      gl.canvas.style.width = width + "px";
      gl.canvas.style.height = height + "px";
      program.uniforms.iResolution.value.set(
        gl.canvas.width,
        gl.canvas.height,
        gl.canvas.width / gl.canvas.height
      );
    }
    window.addEventListener("resize", resize);
    resize();

    let targetHover = 0;
    let lastTime = 0;
    let currentRot = 0;
    const rotationSpeed = 0.3;

    const handleMouseMove = (e: MouseEvent) => {
      const rect = container.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const width = rect.width;
      const height = rect.height;
      const size = Math.min(width, height);
      const centerX = width / 2;
      const centerY = height / 2;
      const uvX = ((x - centerX) / size) * 2.0;
      const uvY = ((y - centerY) / size) * 2.0;

      if (Math.sqrt(uvX * uvX + uvY * uvY) < 0.8) {
        targetHover = 1;
      } else {
        targetHover = 0;
      }
    };

    const handleMouseLeave = () => {
      targetHover = 0;
    };

    container.addEventListener("mousemove", handleMouseMove);
    container.addEventListener("mouseleave", handleMouseLeave);

    let rafId: number;
    const update = (t: number) => {
      if (!program || !renderer || !mesh) return;
      rafId = requestAnimationFrame(update);
      const dt = (t - lastTime) * 0.001;
      lastTime = t;
      program.uniforms.iTime.value = t * 0.001;

      const effectiveHover = forceHoverState ? 1 : targetHover;
      program.uniforms.hover.value += (effectiveHover - program.uniforms.hover.value) * 0.1;

      if (rotateOnHover && effectiveHover > 0.5) {
        currentRot += dt * rotationSpeed;
      }
      program.uniforms.rot.value = currentRot;

      renderer.render({ scene: mesh });
    };
    rafId = requestAnimationFrame(update);

    return () => {
      cancelAnimationFrame(rafId);
      window.removeEventListener("resize", resize);
      container.removeEventListener("mousemove", handleMouseMove);
      container.removeEventListener("mouseleave", handleMouseLeave);
      if (container.contains(gl.canvas)) {
        container.removeChild(gl.canvas);
      }
      gl.getExtension("WEBGL_lose_context")?.loseContext();
    };
  }, []); // Only run once

  // Update uniforms when props change (with threshold to prevent micro-updates)
  useEffect(() => {
    if (programRef.current) {
      const hueChanged = Math.abs(hue - lastHueRef.current) > 1.0;
      const intensityChanged = Math.abs(hoverIntensity - lastIntensityRef.current) > 0.01;
      const voiceStateChanged = voiceState !== lastVoiceStateRef.current;
      const emotionIntensityChanged = Math.abs(emotionIntensity - lastEmotionIntensityRef.current) > 0.01;

      if (hueChanged) {
        programRef.current.uniforms.hue.value = hue;
        lastHueRef.current = hue;
      }

      if (intensityChanged) {
        programRef.current.uniforms.hoverIntensity.value = hoverIntensity;
        lastIntensityRef.current = hoverIntensity;
      }

      if (voiceStateChanged) {
        programRef.current.uniforms.voiceStateFloat.value = voiceState === 'listening' ? 1.0 : 0.0;
        lastVoiceStateRef.current = voiceState;
      }

      if (emotionIntensityChanged) {
        programRef.current.uniforms.emotionIntensity.value = emotionIntensity;
        lastEmotionIntensityRef.current = emotionIntensity;
      }
    }
  }, [hue, hoverIntensity, voiceState, emotionIntensity]);

  return (
    <div
      ref={ref}
      className={cn("w-full h-full", className)}
      {...domProps}
    >
      <div ref={ctnDom} className="w-full h-full" />
    </div>
  );
});

Orb.displayName = 'Orb';

export default Orb;